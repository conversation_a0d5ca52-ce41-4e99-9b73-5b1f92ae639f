import { ProjectFiles, SerializedProjectFiles, SerializedFile } from '@/types/formTypes';

// Convert File to base64 string
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      // Remove the data URL prefix (e.g., "data:image/png;base64,")
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
  });
};

// Convert base64 string back to File
const base64ToFile = (serializedFile: SerializedFile): File => {
  // Convert base64 to binary
  const byteCharacters = atob(serializedFile.data);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  
  // Create File object
  const file = new File([byteArray], serializedFile.name, {
    type: serializedFile.type,
    lastModified: serializedFile.lastModified,
  });
  
  return file;
};

// Determine file category based on type
const getFileCategory = (fileType: string): 'floorplans' | 'references' | 'general' => {
  const SUPPORTED_FILE_TYPES = {
    // Images (for floorplans and references)
    'image/jpeg': 'floorplans',
    'image/jpg': 'floorplans',
    'image/png': 'floorplans',
    'image/gif': 'references',
    'image/bmp': 'references',
    'image/webp': 'references',
    
    // CAD and Design files
    'application/dwg': 'floorplans',
    'application/dxf': 'floorplans',
  } as const;

  return (SUPPORTED_FILE_TYPES[fileType as keyof typeof SUPPORTED_FILE_TYPES] as 'floorplans' | 'references') || 'general';
};

// Serialize ProjectFiles to SerializedProjectFiles
export const serializeProjectFiles = async (projectFiles: ProjectFiles): Promise<SerializedProjectFiles> => {
  const serialized: SerializedProjectFiles = {
    floorplans: [],
    references: [],
    general: []
  };

  // Process floorplans
  for (const file of projectFiles.floorplans) {
    try {
      const data = await fileToBase64(file);
      serialized.floorplans.push({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        data,
        category: 'floorplans'
      });
    } catch (error) {
      console.error('Error serializing floorplan file:', file.name, error);
    }
  }

  // Process references
  for (const file of projectFiles.references) {
    try {
      const data = await fileToBase64(file);
      serialized.references.push({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        data,
        category: 'references'
      });
    } catch (error) {
      console.error('Error serializing reference file:', file.name, error);
    }
  }

  // Process general files
  for (const file of projectFiles.general || []) {
    try {
      const data = await fileToBase64(file);
      serialized.general.push({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        data,
        category: 'general'
      });
    } catch (error) {
      console.error('Error serializing general file:', file.name, error);
    }
  }

  return serialized;
};

// Deserialize SerializedProjectFiles back to ProjectFiles
export const deserializeProjectFiles = (serializedFiles: SerializedProjectFiles): ProjectFiles => {
  const projectFiles: ProjectFiles = {
    floorplans: [],
    references: [],
    general: []
  };

  // Process floorplans
  for (const serializedFile of serializedFiles.floorplans) {
    try {
      const file = base64ToFile(serializedFile);
      projectFiles.floorplans.push(file);
    } catch (error) {
      console.error('Error deserializing floorplan file:', serializedFile.name, error);
    }
  }

  // Process references
  for (const serializedFile of serializedFiles.references) {
    try {
      const file = base64ToFile(serializedFile);
      projectFiles.references.push(file);
    } catch (error) {
      console.error('Error deserializing reference file:', serializedFile.name, error);
    }
  }

  // Process general files
  for (const serializedFile of serializedFiles.general) {
    try {
      const file = base64ToFile(serializedFile);
      projectFiles.general.push(file);
    } catch (error) {
      console.error('Error deserializing general file:', serializedFile.name, error);
    }
  }

  return projectFiles;
};

// Get file count for display purposes
export const getFileCount = (projectFiles: ProjectFiles | SerializedProjectFiles): number => {
  const floorplansCount = projectFiles.floorplans?.length || 0;
  const referencesCount = projectFiles.references?.length || 0;
  const generalCount = projectFiles.general?.length || 0;
  return floorplansCount + referencesCount + generalCount;
};

// Get file summary for display
export const getFileSummary = (projectFiles: ProjectFiles | SerializedProjectFiles): string => {
  const count = getFileCount(projectFiles);
  if (count === 0) return 'No files uploaded';
  if (count === 1) return '1 file uploaded';
  return `${count} files uploaded`;
};
