
import { FormData, SerializedProjectFiles } from "../types/formTypes";
import { serializeProjectFiles, deserializeProjectFiles } from "./fileSerializationService";

export interface Walkthrough {
  id: string;
  title: string;
  client_name: string;
  form_data: FormData;
  last_step: number;
  created_at: string;
  updated_at: string;
}

interface StoredWalkthrough {
  id: string;
  title: string;
  client_name: string;
  form_data: Omit<FormData, 'projectFiles'> & {
    projectFiles?: SerializedProjectFiles;
  };
  last_step: number;
  created_at: string;
  updated_at: string;
}

// Get a specific walkthrough by ID
export const getWalkthroughById = async (id: string): Promise<Walkthrough | null> => {
  try {
    // In a real implementation, this would call an API endpoint
    const storedWalkthroughs: StoredWalkthrough[] = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    const storedWalkthrough = storedWalkthroughs.find((w: StoredWalkthrough) => w.id === id);

    if (!storedWalkthrough) return null;

    // Convert stored walkthrough back to regular walkthrough with File objects
    const walkthrough: Walkthrough = {
      ...storedWalkthrough,
      form_data: {
        ...storedWalkthrough.form_data,
        projectFiles: storedWalkthrough.form_data.projectFiles
          ? deserializeProjectFiles(storedWalkthrough.form_data.projectFiles)
          : { floorplans: [], references: [], general: [] }
      }
    };

    return walkthrough;
  } catch (error) {
    console.error("Error getting walkthrough by ID:", error);
    return null;
  }
};

// Export getWalkthroughById as getWalkthrough for backward compatibility
export const getWalkthrough = getWalkthroughById;

// Get all walkthroughs
export const getWalkthroughs = async (): Promise<Walkthrough[]> => {
  try {
    // In a real implementation, this would call an API endpoint
    const storedWalkthroughs: StoredWalkthrough[] = JSON.parse(localStorage.getItem('walkthroughs') || '[]');

    // Convert all stored walkthroughs back to regular walkthroughs with File objects
    const walkthroughs: Walkthrough[] = storedWalkthroughs.map(storedWalkthrough => ({
      ...storedWalkthrough,
      form_data: {
        ...storedWalkthrough.form_data,
        projectFiles: storedWalkthrough.form_data.projectFiles
          ? deserializeProjectFiles(storedWalkthrough.form_data.projectFiles)
          : { floorplans: [], references: [], general: [] }
      }
    }));

    return walkthroughs;
  } catch (error) {
    console.error("Error getting walkthroughs:", error);
    return [];
  }
};

// Save a new walkthrough
export const saveWalkthrough = async (title: string, formData: FormData): Promise<string | null> => {
  try {
    const id = `walkthrough-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Serialize project files for storage
    const serializedProjectFiles = formData.projectFiles
      ? await serializeProjectFiles(formData.projectFiles)
      : { floorplans: [], references: [], general: [] };

    const storedWalkthrough: StoredWalkthrough = {
      id,
      title,
      client_name: formData.clientInfo.fullName || "Unnamed Client",
      form_data: {
        ...formData,
        projectFiles: serializedProjectFiles
      },
      last_step: formData.currentStep,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const storedWalkthroughs: StoredWalkthrough[] = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    storedWalkthroughs.push(storedWalkthrough);
    localStorage.setItem('walkthroughs', JSON.stringify(storedWalkthroughs));

    return id;
  } catch (error) {
    console.error("Error saving walkthrough:", error);
    return null;
  }
};

// Update an existing walkthrough
export const updateWalkthrough = async (id: string, title: string, formData: FormData): Promise<boolean> => {
  try {
    const storedWalkthroughs: StoredWalkthrough[] = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    const index = storedWalkthroughs.findIndex((w: StoredWalkthrough) => w.id === id);

    if (index === -1) {
      return false;
    }

    // Serialize project files for storage
    const serializedProjectFiles = formData.projectFiles
      ? await serializeProjectFiles(formData.projectFiles)
      : { floorplans: [], references: [], general: [] };

    storedWalkthroughs[index] = {
      ...storedWalkthroughs[index],
      title,
      client_name: formData.clientInfo.fullName || storedWalkthroughs[index].client_name,
      form_data: {
        ...formData,
        projectFiles: serializedProjectFiles
      },
      last_step: formData.currentStep,
      updated_at: new Date().toISOString(),
    };

    localStorage.setItem('walkthroughs', JSON.stringify(storedWalkthroughs));
    return true;
  } catch (error) {
    console.error("Error updating walkthrough:", error);
    return false;
  }
};

// Delete a walkthrough
export const deleteWalkthrough = async (id: string): Promise<boolean> => {
  try {
    const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
    const filteredWalkthroughs = walkthroughs.filter((w: Walkthrough) => w.id !== id);

    localStorage.setItem('walkthroughs', JSON.stringify(filteredWalkthroughs));
    return true;
  } catch (error) {
    console.error("Error deleting walkthrough:", error);
    return false;
  }
};
