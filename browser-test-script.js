// Browser console test script to verify file persistence
// Copy and paste this into the browser console at http://localhost:8082

console.log('🧪 Starting File Persistence Test...');

async function testFilePersistence() {
  try {
    // Test 1: Create a test file
    console.log('📁 Creating test file...');
    const testContent = 'This is a test file for persistence verification.';
    const testFile = new File([testContent], 'test-persistence.txt', { 
      type: 'text/plain',
      lastModified: Date.now()
    });
    
    console.log('✅ Created test file:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });

    // Test 2: Test base64 conversion
    console.log('🔄 Testing base64 conversion...');
    
    const reader = new FileReader();
    const base64Promise = new Promise((resolve, reject) => {
      reader.onload = () => {
        const result = reader.result;
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(testFile);
    });
    
    const base64Data = await base64Promise;
    console.log('✅ Base64 conversion successful, length:', base64Data.length);

    // Test 3: Test base64 to file conversion
    console.log('🔄 Testing base64 to file conversion...');
    
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    
    const reconstructedFile = new File([byteArray], testFile.name, {
      type: testFile.type,
      lastModified: testFile.lastModified,
    });
    
    console.log('✅ File reconstruction successful:', {
      name: reconstructedFile.name,
      size: reconstructedFile.size,
      type: reconstructedFile.type
    });

    // Test 4: Verify content integrity
    console.log('🔍 Verifying content integrity...');
    const reconstructedContent = await reconstructedFile.text();
    const contentMatches = reconstructedContent === testContent;
    
    console.log('✅ Content integrity:', contentMatches ? 'PASSED' : 'FAILED');
    console.log('Original:', testContent);
    console.log('Reconstructed:', reconstructedContent);

    // Test 5: Test localStorage serialization
    console.log('💾 Testing localStorage serialization...');
    
    const testWalkthrough = {
      id: 'test-persistence-123',
      title: 'File Persistence Test',
      client_name: 'Test Client',
      form_data: {
        projectFiles: {
          floorplans: [],
          references: [],
          general: [{
            name: testFile.name,
            size: testFile.size,
            type: testFile.type,
            lastModified: testFile.lastModified,
            data: base64Data,
            category: 'general'
          }]
        },
        clientInfo: { firstName: 'Test', lastName: 'User' },
        currentStep: 5
      },
      last_step: 5,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Save to localStorage
    localStorage.setItem('test_file_persistence', JSON.stringify(testWalkthrough));
    console.log('✅ Saved to localStorage');

    // Load from localStorage
    const loadedData = JSON.parse(localStorage.getItem('test_file_persistence'));
    console.log('✅ Loaded from localStorage');

    // Verify the loaded data
    const loadedFileData = loadedData.form_data.projectFiles.general[0];
    console.log('✅ Loaded file data:', {
      name: loadedFileData.name,
      size: loadedFileData.size,
      type: loadedFileData.type,
      dataLength: loadedFileData.data.length
    });

    // Test 6: Reconstruct file from loaded data
    console.log('🔄 Reconstructing file from loaded data...');
    
    const loadedByteCharacters = atob(loadedFileData.data);
    const loadedByteNumbers = new Array(loadedByteCharacters.length);
    for (let i = 0; i < loadedByteCharacters.length; i++) {
      loadedByteNumbers[i] = loadedByteCharacters.charCodeAt(i);
    }
    const loadedByteArray = new Uint8Array(loadedByteNumbers);
    
    const finalFile = new File([loadedByteArray], loadedFileData.name, {
      type: loadedFileData.type,
      lastModified: loadedFileData.lastModified,
    });
    
    const finalContent = await finalFile.text();
    const finalContentMatches = finalContent === testContent;
    
    console.log('✅ Final content integrity:', finalContentMatches ? 'PASSED' : 'FAILED');
    console.log('Final content:', finalContent);

    // Clean up
    localStorage.removeItem('test_file_persistence');
    console.log('🧹 Cleaned up test data');

    console.log('🎉 ALL TESTS PASSED! File persistence is working correctly.');
    
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Run the test
testFilePersistence().then(success => {
  console.log('Test result:', success ? '✅ SUCCESS' : '❌ FAILURE');
});

// Also provide a quick test for the actual walkthrough flow
console.log(`
📋 Manual Test Steps:
1. Go to http://localhost:8082/new-walkthrough
2. Fill in client info and advance to Files Upload step
3. Upload a .txt file (should work without errors)
4. Save the walkthrough with a title
5. Navigate away and come back
6. Load the saved walkthrough
7. Check that files are restored in the Files Upload step
8. Check that file count shows in saved walkthroughs dialog
`);
